using System.Diagnostics;
using System.Security.Principal;

namespace NetworkInterceptor;

/// <summary>
/// Helper class for handling UAC (User Account Control) elevation
/// </summary>
public static class UacHelper
{
    /// <summary>
    /// Checks if the current process is running with administrator privileges
    /// </summary>
    /// <returns>True if running as administrator, false otherwise</returns>
    public static bool IsRunningAsAdministrator()
    {
        try
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// Attempts to restart the current application with administrator privileges
    /// </summary>
    /// <param name="args">Command line arguments to pass to the elevated process</param>
    /// <returns>True if the elevation attempt was successful, false otherwise</returns>
    public static bool TryElevateToAdministrator(string[]? args = null)
    {
        try
        {
            // Get the current executable path
            var currentProcess = Process.GetCurrentProcess();
            var executablePath = currentProcess.MainModule?.FileName;
            
            if (string.IsNullOrEmpty(executablePath))
            {
                // Fallback for .NET applications
                executablePath = Environment.ProcessPath;
            }

            if (string.IsNullOrEmpty(executablePath))
            {
                Console.WriteLine("Unable to determine executable path for elevation.");
                return false;
            }

            // Prepare arguments
            var arguments = args != null ? string.Join(" ", args) : string.Empty;

            // Create process start info for elevation
            var startInfo = new ProcessStartInfo
            {
                FileName = executablePath,
                Arguments = arguments,
                UseShellExecute = true,
                Verb = "runas", // This triggers UAC elevation
                WorkingDirectory = Environment.CurrentDirectory
            };

            // For .NET applications, we might need to use dotnet.exe
            if (executablePath.EndsWith(".dll", StringComparison.OrdinalIgnoreCase))
            {
                startInfo.FileName = "dotnet";
                startInfo.Arguments = $"\"{executablePath}\" {arguments}";
            }

            Console.WriteLine("Requesting administrator privileges...");
            Console.WriteLine("Please click 'Yes' in the UAC dialog to continue.");

            // Start the elevated process
            var elevatedProcess = Process.Start(startInfo);
            
            if (elevatedProcess != null)
            {
                Console.WriteLine("Elevated process started successfully.");
                return true;
            }
            else
            {
                Console.WriteLine("Failed to start elevated process.");
                return false;
            }
        }
        catch (System.ComponentModel.Win32Exception ex)
        {
            // User clicked "No" in UAC dialog or other Win32 error
            if (ex.NativeErrorCode == 1223) // ERROR_CANCELLED
            {
                Console.WriteLine("User cancelled the elevation request.");
            }
            else
            {
                Console.WriteLine($"Failed to elevate privileges: {ex.Message}");
            }
            return false;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Unexpected error during elevation: {ex.Message}");
            return false;
        }
    }

    /// <summary>
    /// Prompts the user to restart the application with administrator privileges
    /// </summary>
    /// <param name="args">Command line arguments to pass to the elevated process</param>
    /// <returns>True if user chose to elevate and elevation was successful</returns>
    public static bool PromptForElevation(string[]? args = null)
    {
        Console.WriteLine();
        Console.WriteLine("=== Administrator Privileges Required ===");
        Console.WriteLine("This application requires administrator privileges to intercept network traffic.");
        Console.WriteLine();
        Console.WriteLine("Options:");
        Console.WriteLine("1. Restart with administrator privileges (Recommended)");
        Console.WriteLine("2. Continue without network interception (Limited functionality)");
        Console.WriteLine("3. Exit application");
        Console.WriteLine();
        Console.Write("Please choose an option (1-3): ");

        var choice = Console.ReadLine()?.Trim();

        switch (choice)
        {
            case "1":
                Console.WriteLine();
                if (TryElevateToAdministrator(args))
                {
                    Console.WriteLine("Application will restart with administrator privileges.");
                    Console.WriteLine("You can close this window.");
                    return true;
                }
                else
                {
                    Console.WriteLine("Failed to restart with administrator privileges.");
                    Console.WriteLine("You can try running the application manually as administrator.");
                    return false;
                }

            case "2":
                Console.WriteLine();
                Console.WriteLine("Continuing with limited functionality...");
                Console.WriteLine("Network interception features will be disabled.");
                return false;

            case "3":
            default:
                Console.WriteLine("Exiting application.");
                return false;
        }
    }

    /// <summary>
    /// Gets information about the current user and elevation status
    /// </summary>
    /// <returns>String containing user and elevation information</returns>
    public static string GetElevationInfo()
    {
        try
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            var isAdmin = principal.IsInRole(WindowsBuiltInRole.Administrator);
            var isElevated = IsRunningAsAdministrator();

            return $"User: {identity.Name}, Admin Role: {isAdmin}, Elevated: {isElevated}";
        }
        catch (Exception ex)
        {
            return $"Unable to get elevation info: {ex.Message}";
        }
    }

    /// <summary>
    /// Checks if UAC is enabled on the system
    /// </summary>
    /// <returns>True if UAC is enabled, false otherwise</returns>
    public static bool IsUacEnabled()
    {
        try
        {
            using var key = Microsoft.Win32.Registry.LocalMachine.OpenSubKey(
                @"SOFTWARE\Microsoft\Windows\CurrentVersion\Policies\System");
            
            if (key != null)
            {
                var enableLua = key.GetValue("EnableLUA");
                return enableLua != null && (int)enableLua == 1;
            }
            
            return true; // Default to true if we can't determine
        }
        catch
        {
            return true; // Default to true if we can't access registry
        }
    }

    /// <summary>
    /// Creates a manifest file for UAC elevation (for future use)
    /// </summary>
    /// <param name="manifestPath">Path where to create the manifest file</param>
    public static void CreateElevationManifest(string manifestPath)
    {
        var manifestContent = @"<?xml version=""1.0"" encoding=""UTF-8"" standalone=""yes""?>
<assembly xmlns=""urn:schemas-microsoft-com:asm.v1"" manifestVersion=""1.0"">
  <assemblyIdentity version=""*******"" name=""NetworkInterceptor"" type=""win32"" />
  <description>Network Interceptor Application</description>
  <trustInfo xmlns=""urn:schemas-microsoft-com:asm.v2"">
    <security>
      <requestedPrivileges xmlns=""urn:schemas-microsoft-com:asm.v3"">
        <requestedExecutionLevel level=""requireAdministrator"" uiAccess=""false"" />
      </requestedPrivileges>
    </security>
  </trustInfo>
</assembly>";

        try
        {
            File.WriteAllText(manifestPath, manifestContent);
            Console.WriteLine($"UAC manifest created at: {manifestPath}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to create UAC manifest: {ex.Message}");
        }
    }
}
