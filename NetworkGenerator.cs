using System.Net;
using System.Net.Sockets;
using System.Text;

namespace NetworkGenerator
{
    class Program
    {
        static async Task Main(string[] args)
        {
            Console.WriteLine("=== Network Traffic Generator ===");
            Console.WriteLine("This program generates network traffic for testing");
            Console.WriteLine();

            if (args.Length > 0 && args[0] == "auto")
            {
                Console.WriteLine("🤖 Running in automatic mode...");
                await RunAutomaticTest();
            }
            else
            {
                Console.WriteLine("Manual mode - generating various types of network traffic");
                await RunManualTest();
            }
        }

        static async Task RunAutomaticTest()
        {
            Console.WriteLine("⏱️ Waiting 3 seconds for interceptor to start...");
            await Task.Delay(3000);

            Console.WriteLine("🚀 Starting traffic generation...");
            
            var tasks = new List<Task>
            {
                GenerateHttpTraffic(),
                GenerateUdpTraffic(),
                GenerateTcpTraffic(),
                GenerateHttpsTraffic()
            };

            await Task.WhenAll(tasks);
            
            Console.WriteLine("✅ Traffic generation completed");
            await Task.Delay(2000); // Give interceptor time to process
        }

        static async Task RunManualTest()
        {
            while (true)
            {
                Console.WriteLine("\nSelect traffic type to generate:");
                Console.WriteLine("1. HTTP requests");
                Console.WriteLine("2. UDP packets");
                Console.WriteLine("3. TCP connections");
                Console.WriteLine("4. HTTPS requests");
                Console.WriteLine("5. All types (auto)");
                Console.WriteLine("6. Exit");
                Console.Write("Choice: ");

                var choice = Console.ReadLine();
                
                switch (choice)
                {
                    case "1":
                        await GenerateHttpTraffic();
                        break;
                    case "2":
                        await GenerateUdpTraffic();
                        break;
                    case "3":
                        await GenerateTcpTraffic();
                        break;
                    case "4":
                        await GenerateHttpsTraffic();
                        break;
                    case "5":
                        await RunAutomaticTest();
                        break;
                    case "6":
                        return;
                    default:
                        Console.WriteLine("Invalid choice");
                        break;
                }
            }
        }

        static async Task GenerateHttpTraffic()
        {
            Console.WriteLine("📡 Generating HTTP traffic...");
            
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(5);
                
                var urls = new[]
                {
                    "http://httpbin.org/get",
                    "http://example.com",
                    "http://httpbin.org/json",
                    "http://httpbin.org/user-agent"
                };

                foreach (var url in urls)
                {
                    try
                    {
                        Console.WriteLine($"   🌐 Requesting: {url}");
                        var response = await client.GetAsync(url);
                        Console.WriteLine($"   ✅ Response: {response.StatusCode}");
                        await Task.Delay(500);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ Error: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ HTTP traffic generation failed: {ex.Message}");
            }
        }

        static async Task GenerateUdpTraffic()
        {
            Console.WriteLine("📡 Generating UDP traffic...");
            
            try
            {
                using var udpClient = new UdpClient();
                var data = Encoding.UTF8.GetBytes("Test UDP packet from NetworkGenerator");
                
                // Send to localhost on different ports
                var ports = new[] { 12345, 8080, 9999 };
                
                foreach (var port in ports)
                {
                    try
                    {
                        Console.WriteLine($"   📤 Sending UDP to localhost:{port}");
                        await udpClient.SendAsync(data, data.Length, "127.0.0.1", port);
                        await Task.Delay(200);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ UDP Error: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ UDP traffic generation failed: {ex.Message}");
            }
        }

        static async Task GenerateTcpTraffic()
        {
            Console.WriteLine("📡 Generating TCP traffic...");
            
            var ports = new[] { 80, 8080, 9999 };
            
            foreach (var port in ports)
            {
                try
                {
                    Console.WriteLine($"   🔌 Connecting to localhost:{port}");
                    using var tcpClient = new TcpClient();
                    
                    var connectTask = tcpClient.ConnectAsync("127.0.0.1", port);
                    var timeoutTask = Task.Delay(2000);
                    
                    var completedTask = await Task.WhenAny(connectTask, timeoutTask);
                    
                    if (completedTask == connectTask && tcpClient.Connected)
                    {
                        Console.WriteLine($"   ✅ Connected to port {port}");
                        
                        var stream = tcpClient.GetStream();
                        var data = Encoding.UTF8.GetBytes("GET / HTTP/1.1\r\nHost: localhost\r\n\r\n");
                        await stream.WriteAsync(data, 0, data.Length);
                        
                        tcpClient.Close();
                    }
                    else
                    {
                        Console.WriteLine($"   ⏱️ Connection timeout to port {port}");
                    }
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"   ❌ TCP Error: {ex.Message}");
                }
                
                await Task.Delay(300);
            }
        }

        static async Task GenerateHttpsTraffic()
        {
            Console.WriteLine("📡 Generating HTTPS traffic...");
            
            try
            {
                using var client = new HttpClient();
                client.Timeout = TimeSpan.FromSeconds(5);
                
                var urls = new[]
                {
                    "https://httpbin.org/get",
                    "https://jsonplaceholder.typicode.com/posts/1",
                    "https://api.github.com"
                };

                foreach (var url in urls)
                {
                    try
                    {
                        Console.WriteLine($"   🔒 HTTPS Request: {url}");
                        var response = await client.GetAsync(url);
                        Console.WriteLine($"   ✅ HTTPS Response: {response.StatusCode}");
                        await Task.Delay(500);
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"   ❌ HTTPS Error: {ex.Message}");
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ HTTPS traffic generation failed: {ex.Message}");
            }
        }
    }
}
