using NetworkInterceptor;
using NetworkInterceptor.Models;
using System.Security.Principal;

class Program
{
    private static ProcessManager? _processManager;
    private static NetworkCapture? _networkCapture;
    private static CertificateManager? _certificateManager;
    private static readonly List<ProcessInfo> _selectedProcesses = new();

    static async Task Main(string[] args)
    {
        Console.WriteLine("=== Network Interceptor ===");
        Console.WriteLine("A .NET Core 9 application for intercepting and modifying network traffic");
        Console.WriteLine();

        // Check if running as administrator
        if (!IsRunningAsAdministrator())
        {
            Console.WriteLine("ERROR: This application requires administrator privileges to intercept network traffic.");
            Console.WriteLine("Please run as administrator and try again.");
            Console.ReadKey();
            return;
        }

        try
        {
            await InitializeComponents();
            await RunMainLoop();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Fatal error: {ex.Message}");
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }
        finally
        {
            Cleanup();
        }
    }

    private static bool IsRunningAsAdministrator()
    {
        var identity = WindowsIdentity.GetCurrent();
        var principal = new WindowsPrincipal(identity);
        return principal.IsInRole(WindowsBuiltInRole.Administrator);
    }

    private static Task InitializeComponents()
    {
        Console.WriteLine("Initializing components...");

        _processManager = new ProcessManager();
        _networkCapture = new NetworkCapture();
        _certificateManager = new CertificateManager();

        // Set up event handlers
        _networkCapture.PacketIntercepted += OnPacketIntercepted;
        _networkCapture.ErrorOccurred += OnErrorOccurred;

        Console.WriteLine("Components initialized successfully.");
        Console.WriteLine();

        return Task.CompletedTask;
    }

    private static async Task RunMainLoop()
    {
        bool running = true;

        while (running)
        {
            ShowMainMenu();
            var choice = Console.ReadLine()?.Trim();

            switch (choice)
            {
                case "1":
                    await ListProcesses();
                    break;
                case "2":
                    await SelectProcess();
                    break;
                case "3":
                    await ShowSelectedProcesses();
                    break;
                case "4":
                    await ConfigureInterceptionRules();
                    break;
                case "5":
                    await StartInterception();
                    break;
                case "6":
                    StopInterception();
                    break;
                case "7":
                    await ManageCertificates();
                    break;
                case "8":
                    await ShowStatistics();
                    break;
                case "9":
                case "q":
                case "quit":
                case "exit":
                    running = false;
                    break;
                default:
                    Console.WriteLine("Invalid choice. Please try again.");
                    break;
            }

            if (running)
            {
                Console.WriteLine("\nPress any key to continue...");
                Console.ReadKey();
                Console.Clear();
            }
        }
    }

    private static void ShowMainMenu()
    {
        Console.WriteLine("=== Main Menu ===");
        Console.WriteLine("1. List all processes");
        Console.WriteLine("2. Select process for interception");
        Console.WriteLine("3. Show selected processes");
        Console.WriteLine("4. Configure interception rules");
        Console.WriteLine("5. Start network interception");
        Console.WriteLine("6. Stop network interception");
        Console.WriteLine("7. Manage certificates (for HTTPS)");
        Console.WriteLine("8. Show statistics");
        Console.WriteLine("9. Exit");
        Console.WriteLine();
        Console.Write("Enter your choice: ");
    }

    private static Task ListProcesses()
    {
        Console.WriteLine("\n=== Process List ===");

        if (_processManager == null)
        {
            Console.WriteLine("Process manager not initialized.");
            return Task.CompletedTask;
        }

        var processes = _processManager.GetAllProcesses();

        Console.WriteLine($"{"PID",-8} {"Name",-25} {"Connections",-12} {"Memory (MB)",-12}");
        Console.WriteLine(new string('-', 70));

        foreach (var process in processes.Take(50)) // Show first 50 processes
        {
            var memoryMB = process.WorkingSet / (1024 * 1024);
            Console.WriteLine($"{process.ProcessId,-8} {process.ProcessName,-25} {process.Connections.Count,-12} {memoryMB,-12}");
        }

        if (processes.Count > 50)
        {
            Console.WriteLine($"\n... and {processes.Count - 50} more processes");
        }

        return Task.CompletedTask;
    }

    private static Task SelectProcess()
    {
        Console.WriteLine("\n=== Select Process ===");
        Console.Write("Enter process ID or name: ");
        var input = Console.ReadLine()?.Trim();

        if (string.IsNullOrEmpty(input))
        {
            Console.WriteLine("Invalid input.");
            return Task.CompletedTask;
        }

        if (_processManager == null)
        {
            Console.WriteLine("Process manager not initialized.");
            return Task.CompletedTask;
        }

        ProcessInfo? process = null;

        if (int.TryParse(input, out int processId))
        {
            process = _processManager.GetProcessById(processId);
        }
        else
        {
            var processes = _processManager.GetProcessesByName(input);
            if (processes.Count == 1)
            {
                process = processes[0];
            }
            else if (processes.Count > 1)
            {
                Console.WriteLine($"Multiple processes found with name '{input}':");
                for (int i = 0; i < processes.Count; i++)
                {
                    Console.WriteLine($"{i + 1}. PID: {processes[i].ProcessId} - {processes[i].ProcessName}");
                }
                Console.Write("Select process number: ");
                if (int.TryParse(Console.ReadLine(), out int selection) && 
                    selection > 0 && selection <= processes.Count)
                {
                    process = processes[selection - 1];
                }
            }
        }

        if (process != null)
        {
            if (!_selectedProcesses.Any(p => p.ProcessId == process.ProcessId))
            {
                _selectedProcesses.Add(process);
                _networkCapture?.AddTargetProcess(process);
                Console.WriteLine($"Process '{process.ProcessName}' (PID: {process.ProcessId}) added to interception list.");
            }
            else
            {
                Console.WriteLine("Process already selected.");
            }
        }
        else
        {
            Console.WriteLine("Process not found.");
        }

        return Task.CompletedTask;
    }

    private static Task ShowSelectedProcesses()
    {
        Console.WriteLine("\n=== Selected Processes ===");

        if (_selectedProcesses.Count == 0)
        {
            Console.WriteLine("No processes selected for interception.");
            return Task.CompletedTask;
        }

        Console.WriteLine($"{"PID",-8} {"Name",-25} {"Connections",-12} {"Status",-10}");
        Console.WriteLine(new string('-', 60));

        foreach (var process in _selectedProcesses)
        {
            var isRunning = _processManager?.IsProcessRunning(process.ProcessId) ?? false;
            var status = isRunning ? "Running" : "Stopped";
            Console.WriteLine($"{process.ProcessId,-8} {process.ProcessName,-25} {process.Connections.Count,-12} {status,-10}");
        }

        return Task.CompletedTask;
    }

    private static Task ConfigureInterceptionRules()
    {
        Console.WriteLine("\n=== Configure Interception Rules ===");
        Console.WriteLine("This feature allows you to set up rules for packet modification.");
        Console.WriteLine("Implementation details would include:");
        Console.WriteLine("- Protocol filtering (TCP/UDP)");
        Console.WriteLine("- Port-based filtering");
        Console.WriteLine("- Content pattern matching and replacement");
        Console.WriteLine("- Direction-based rules (inbound/outbound)");
        Console.WriteLine("\nFor now, basic interception is enabled for all selected processes.");

        return Task.CompletedTask;
    }

    private static Task StartInterception()
    {
        Console.WriteLine("\n=== Starting Network Interception ===");

        if (_selectedProcesses.Count == 0)
        {
            Console.WriteLine("No processes selected. Please select processes first.");
            return Task.CompletedTask;
        }

        if (_networkCapture?.IsCapturing == true)
        {
            Console.WriteLine("Interception is already running.");
            return Task.CompletedTask;
        }

        try
        {
            _networkCapture?.StartCapture();
            Console.WriteLine("Network interception started successfully.");
            Console.WriteLine("Monitoring traffic for selected processes...");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to start interception: {ex.Message}");
        }

        return Task.CompletedTask;
    }

    private static void StopInterception()
    {
        Console.WriteLine("\n=== Stopping Network Interception ===");
        
        if (_networkCapture?.IsCapturing != true)
        {
            Console.WriteLine("Interception is not running.");
            return;
        }

        _networkCapture.StopCapture();
        Console.WriteLine("Network interception stopped.");
    }

    private static Task ManageCertificates()
    {
        Console.WriteLine("\n=== Certificate Management ===");
        Console.WriteLine("1. Install root certificate (required for HTTPS interception)");
        Console.WriteLine("2. Generate server certificate");
        Console.WriteLine("3. Uninstall root certificate");
        Console.WriteLine("4. Show certificate info");
        Console.Write("Enter choice: ");

        var choice = Console.ReadLine()?.Trim();
        
        switch (choice)
        {
            case "1":
                try
                {
                    _certificateManager?.InstallRootCertificate();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error installing certificate: {ex.Message}");
                }
                break;
            case "2":
                Console.Write("Enter hostname for server certificate: ");
                var hostname = Console.ReadLine()?.Trim();
                if (!string.IsNullOrEmpty(hostname))
                {
                    try
                    {
                        var cert = _certificateManager?.GenerateServerCertificate(hostname);
                        Console.WriteLine($"Server certificate generated for {hostname}");
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine($"Error generating certificate: {ex.Message}");
                    }
                }
                break;
            case "3":
                try
                {
                    _certificateManager?.UninstallRootCertificate();
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"Error uninstalling certificate: {ex.Message}");
                }
                break;
            case "4":
                var rootCert = _certificateManager?.GetRootCertificate();
                if (rootCert != null)
                {
                    Console.WriteLine($"Root Certificate Subject: {rootCert.Subject}");
                    Console.WriteLine($"Valid From: {rootCert.NotBefore}");
                    Console.WriteLine($"Valid To: {rootCert.NotAfter}");
                    Console.WriteLine($"Thumbprint: {rootCert.Thumbprint}");
                }
                else
                {
                    Console.WriteLine("No root certificate available.");
                }
                break;
        }

        return Task.CompletedTask;
    }

    private static Task ShowStatistics()
    {
        Console.WriteLine("\n=== Statistics ===");
        Console.WriteLine($"Selected Processes: {_selectedProcesses.Count}");
        Console.WriteLine($"Interception Status: {(_networkCapture?.IsCapturing == true ? "Running" : "Stopped")}");
        Console.WriteLine($"Certificate Manager: {(_certificateManager != null ? "Initialized" : "Not Available")}");

        // Additional statistics could be added here
        Console.WriteLine("\nNote: Detailed packet statistics would be implemented in a production version.");

        return Task.CompletedTask;
    }

    private static void OnPacketIntercepted(object? sender, InterceptedPacket packet)
    {
        // In a real application, you might want to log this to a file or display in a separate window
        // For now, we'll just count them or display minimal info
        if (_networkCapture?.IsCapturing == true)
        {
            Console.WriteLine($"[{packet.Timestamp:HH:mm:ss}] {packet.Protocol} {packet.SourceAddress}:{packet.SourcePort} -> {packet.DestinationAddress}:{packet.DestinationPort}");
        }
    }

    private static void OnErrorOccurred(object? sender, string error)
    {
        Console.WriteLine($"ERROR: {error}");
    }

    private static void Cleanup()
    {
        Console.WriteLine("\nCleaning up...");
        _networkCapture?.Dispose();
        Console.WriteLine("Cleanup completed.");
    }
}
