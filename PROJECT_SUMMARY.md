# Network Interceptor - 项目总结

## 项目概述

这是一个基于 .NET Core 9 的网络流量拦截和修改工具，专门设计用于 Windows 平台。该项目实现了进程级别的网络监控，支持 UDP/TCP 协议拦截，并包含 HTTPS 流量处理能力。

## 已实现的功能

### ✅ 核心功能
- **进程管理**: 完整的 Windows 进程枚举和选择功能
- **网络拦截**: 基于 WinDivert 的高性能包拦截
- **协议支持**: TCP/UDP 协议解析和修改
- **证书管理**: 自动生成和管理 SSL/TLS 证书用于 HTTPS 拦截
- **规则引擎**: 灵活的拦截规则配置系统

### ✅ 技术实现
- **WinDivert 集成**: 通过 P/Invoke 调用原生 WinDivert 库
- **包解析**: 使用 SharpPcap 和 PacketDotNet 进行网络包处理
- **证书生成**: 使用 BouncyCastle 实现 PKI 功能
- **异步处理**: 多线程网络包处理架构

## 项目结构

```
NetworkInterceptor/
├── Models/
│   └── ProcessInfo.cs          # 数据模型 (进程信息、网络连接、拦截包等)
├── Native/                     # WinDivert 原生库目录
│   ├── README.txt             # WinDivert 安装说明
│   ├── WinDivert.dll          # (需要下载)
│   └── WinDivert.sys          # (需要下载)
├── certificates/               # 证书存储 (运行时创建)
├── NetworkCapture.cs           # 网络拦截核心类
├── ProcessManager.cs           # 进程管理类
├── WinDivertWrapper.cs         # WinDivert P/Invoke 包装
├── CertificateManager.cs       # 证书管理类
├── Program.cs                  # 主程序入口和用户界面
├── NetworkInterceptor.csproj   # 项目配置文件
├── appsettings.json           # 应用程序配置
├── setup-windivert.ps1        # WinDivert 自动下载脚本
├── setup-windivert.bat        # 批处理启动器
├── test-build.ps1             # 构建测试脚本
└── README.md                  # 详细使用说明
```

## 技术栈

### C# 依赖包
- **SharpPcap 6.3.0**: 跨平台网络包捕获框架
- **PacketDotNet 1.4.8**: 网络包解析和构造库
- **BouncyCastle.Cryptography 2.3.1**: 加密和证书管理
- **System.Management 8.0.0**: Windows 系统管理接口

### 原生库依赖
- **WinDivert 2.2.2+**: Windows 网络包拦截驱动

## 使用流程

### 1. 环境准备
```bash
# 1. 下载 WinDivert (以管理员身份运行)
.\setup-windivert.bat

# 2. 编译项目
dotnet build -c Release

# 3. 以管理员身份运行
dotnet run
```

### 2. 基本操作
1. **选择目标进程**: 从进程列表中选择要监控的应用程序
2. **配置拦截规则**: 设置协议过滤、端口过滤等条件
3. **安装证书**: 为 HTTPS 拦截安装根证书
4. **开始拦截**: 启动实时网络流量监控
5. **查看结果**: 实时查看拦截到的网络包

### 3. HTTPS 支持
- 自动生成根证书和服务器证书
- 支持动态域名证书生成
- 证书自动安装到系统信任存储

## 技术亮点

### 1. 高性能网络拦截
- 使用 WinDivert 内核驱动，性能优于用户态方案
- 支持零拷贝包处理
- 多线程异步处理架构

### 2. 灵活的规则引擎
- 支持进程级别过滤
- 协议和端口级别过滤
- 内容模式匹配和替换
- 方向性规则 (入站/出站)

### 3. 完整的 HTTPS 支持
- 动态证书生成
- 透明代理模式
- TLS 终止和重新加密

### 4. 用户友好的界面
- 清晰的控制台菜单系统
- 实时状态显示
- 详细的错误处理和提示

## 安全考虑

### 权限要求
- **管理员权限**: 加载内核驱动必需
- **证书权限**: 安装根证书到系统存储

### 安全警告
- 仅用于合法的网络分析和调试
- 请勿用于恶意攻击或未授权监听
- 使用前确保符合当地法律法规

## 扩展可能性

### 1. 功能扩展
- **GUI 界面**: 使用 WPF 或 WinUI 3 开发图形界面
- **更多协议**: 支持 ICMP、IPv6 等协议
- **数据库存储**: 持久化拦截数据和规则
- **插件系统**: 支持自定义处理插件

### 2. 性能优化
- **包缓冲**: 批量处理网络包
- **内存池**: 减少 GC 压力
- **DPDK 集成**: 更高性能的包处理

### 3. 平台扩展
- **Linux 支持**: 使用 netfilterqueue 或 eBPF
- **macOS 支持**: 使用 pfctl 或类似技术

## 已知限制

### 技术限制
1. **仅支持 Windows**: 依赖 WinDivert 驱动
2. **需要管理员权限**: 内核驱动加载要求
3. **进程关联**: 网络层难以直接关联到具体进程
4. **性能影响**: 高流量环境下可能影响网络性能

### 兼容性
- **Windows 版本**: Windows 7+ (推荐 Windows 10+)
- **架构**: 仅支持 x64
- **防病毒软件**: 可能被误报为恶意软件

## 故障排除

### 常见问题
1. **"Failed to open WinDivert handle"**
   - 确保以管理员身份运行
   - 检查 WinDivert 文件是否存在
   - 确认防病毒软件未阻止

2. **证书安装失败**
   - 检查用户权限
   - 确认证书存储访问权限

3. **包拦截不工作**
   - 验证进程选择是否正确
   - 检查网络连接是否活跃
   - 确认拦截规则配置

## 开发环境

### 要求
- **Visual Studio 2022** 或 **VS Code**
- **.NET 9.0 SDK**
- **Windows 10/11 开发环境**
- **管理员权限** (用于测试)

### 构建命令
```bash
# 还原包
dotnet restore

# 编译
dotnet build -c Release

# 运行测试
.\test-build.ps1

# 发布
dotnet publish -c Release -r win-x64 --self-contained
```

## 贡献指南

### 代码规范
- 遵循 C# 编码规范
- 使用有意义的变量和方法名
- 添加适当的注释和文档

### 提交流程
1. Fork 项目
2. 创建功能分支
3. 提交代码更改
4. 创建 Pull Request

## 许可证

本项目仅供学习和研究使用。商业使用请联系作者获得授权。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。

---

**注意**: 这是一个教育性项目，展示了网络拦截技术的实现原理。在实际使用中，请确保遵守相关法律法规和道德准则。
