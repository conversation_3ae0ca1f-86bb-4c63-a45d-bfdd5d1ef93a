# 快速开始指南

## 🚀 5分钟快速启动

### 步骤 1: 下载 WinDivert
```bash
# 以管理员身份运行 PowerShell 或命令提示符
# 右键点击 setup-windivert.bat -> "以管理员身份运行"
.\setup-windivert.bat
```

### 步骤 2: 编译项目
```bash
dotnet build -c Release
```

### 步骤 3: 运行应用程序
```bash
# 必须以管理员身份运行
dotnet run
```

## 📋 基本使用流程

### 1. 选择目标进程
- 启动应用后，选择菜单选项 `1` 查看所有进程
- 选择菜单选项 `2` 输入进程ID或名称来选择要监控的进程

### 2. 开始拦截
- 选择菜单选项 `5` 开始网络拦截
- 应用程序将显示实时的网络流量信息

### 3. HTTPS 支持 (可选)
- 选择菜单选项 `7` 进入证书管理
- 选择 `1` 安装根证书 (用于HTTPS拦截)
- 选择 `2` 为特定域名生成服务器证书

## 🔧 系统要求

- **操作系统**: Windows 10/11 (x64)
- **运行时**: .NET 9.0
- **权限**: 管理员权限 (必需)
- **内存**: 至少 512MB 可用内存

## ⚠️ 重要提醒

1. **管理员权限**: 必须以管理员身份运行，否则无法加载网络驱动
2. **防病毒软件**: 某些防病毒软件可能会阻止 WinDivert，需要添加例外
3. **合法使用**: 仅用于合法的网络分析和调试目的

## 🐛 常见问题

### Q: "Failed to open WinDivert handle" 错误
**A**: 确保以管理员身份运行，并检查 WinDivert 文件是否存在于 Native 目录

### Q: 无法看到网络流量
**A**: 确保选择了正在活跃使用网络的进程，并且该进程有网络连接

### Q: 证书安装失败
**A**: 确保有足够的权限访问系统证书存储

## 📚 更多信息

- 详细文档: [README.md](README.md)
- 项目总结: [PROJECT_SUMMARY.md](PROJECT_SUMMARY.md)
- 技术实现: 查看源代码注释

## 🎯 示例使用场景

### 监控浏览器流量
1. 启动应用程序
2. 选择浏览器进程 (如 chrome.exe, firefox.exe)
3. 开始拦截
4. 在浏览器中访问网站，观察流量

### 分析应用程序网络行为
1. 启动目标应用程序
2. 在网络拦截器中选择该进程
3. 配置拦截规则 (可选)
4. 开始监控网络活动

### HTTPS 流量分析
1. 安装根证书
2. 为目标域名生成服务器证书
3. 配置应用程序使用代理 (需要额外配置)
4. 开始拦截加密流量

---

**开始使用**: 运行 `.\setup-windivert.bat` 然后 `dotnet run` (以管理员身份)
