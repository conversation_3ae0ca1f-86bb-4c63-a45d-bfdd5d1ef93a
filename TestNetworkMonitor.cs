using System.Diagnostics;
using System.Security.Principal;
using NetworkInterceptor;
using NetworkInterceptor.Models;

class TestNetworkMonitor
{
    static void Main(string[] args)
    {
        Console.WriteLine("=== Network Monitor Test ===");
        Console.WriteLine("Testing Chrome network monitoring functionality");
        Console.WriteLine();

        // Check admin privileges
        var isAdmin = UacHelper.IsRunningAsAdministrator();
        Console.WriteLine($"Administrator privileges: {(isAdmin ? "✅ Yes" : "❌ No")}");
        
        if (!isAdmin)
        {
            Console.WriteLine("This test requires administrator privileges.");
            Console.WriteLine("Please run as administrator.");
            Console.ReadKey();
            return;
        }

        // Check WinDivert files
        var winDivertDll = Path.Combine("Native", "WinDivert.dll");
        var winDivertSys = Path.Combine("Native", "WinDivert.sys");
        
        Console.WriteLine($"WinDivert.dll: {(File.Exists(winDivertDll) ? "✅ Found" : "❌ Missing")}");
        Console.WriteLine($"WinDivert.sys: {(File.Exists(winDivertSys) ? "✅ Found" : "❌ Missing")}");
        
        if (!File.Exists(winDivertDll) || !File.Exists(winDivertSys))
        {
            Console.WriteLine("WinDivert files are missing. Please run setup-windivert.ps1 first.");
            Console.ReadKey();
            return;
        }

        Console.WriteLine();
        Console.WriteLine("Initializing components...");

        try
        {
            var processManager = new ProcessManager();
            var networkCapture = new NetworkCapture();

            // Set up event handler for packet interception
            int packetCount = 0;
            networkCapture.PacketIntercepted += (sender, packet) =>
            {
                packetCount++;
                Console.WriteLine($"[{packet.Timestamp:HH:mm:ss.fff}] {packet.Protocol} " +
                                $"{packet.SourceAddress}:{packet.SourcePort} -> " +
                                $"{packet.DestinationAddress}:{packet.DestinationPort} " +
                                $"({packet.Data.Length} bytes)");
                
                // Limit output to prevent spam
                if (packetCount > 50)
                {
                    Console.WriteLine("... (limiting output, press any key to stop)");
                    packetCount = 0;
                }
            };

            networkCapture.ErrorOccurred += (sender, error) =>
            {
                Console.WriteLine($"❌ Error: {error}");
            };

            Console.WriteLine("✅ Components initialized");
            Console.WriteLine();

            // Find Chrome processes
            Console.WriteLine("Looking for Chrome processes...");
            var chromeProcesses = processManager.GetProcessesByName("chrome");
            
            if (chromeProcesses.Count == 0)
            {
                Console.WriteLine("❌ No Chrome processes found.");
                Console.WriteLine("Please start Chrome and try again.");
                Console.ReadKey();
                return;
            }

            Console.WriteLine($"✅ Found {chromeProcesses.Count} Chrome process(es):");
            foreach (var proc in chromeProcesses.Take(5))
            {
                Console.WriteLine($"   PID: {proc.ProcessId}, Memory: {proc.WorkingSet / (1024 * 1024)}MB");
            }

            // Select the first Chrome process
            var targetProcess = chromeProcesses.First();
            Console.WriteLine($"📌 Targeting Chrome process PID: {targetProcess.ProcessId}");
            
            networkCapture.AddTargetProcess(targetProcess);
            Console.WriteLine();

            Console.WriteLine("🚀 Starting network interception...");
            Console.WriteLine("   Open Chrome and browse to some websites to see network traffic.");
            Console.WriteLine("   Press any key to stop monitoring.");
            Console.WriteLine();

            try
            {
                networkCapture.StartCapture();
                Console.WriteLine("✅ Network interception started successfully!");
                Console.WriteLine("📡 Monitoring network traffic...");
                Console.WriteLine();

                // Wait for user input to stop
                Console.ReadKey();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Failed to start network interception: {ex.Message}");
                Console.WriteLine("   This might be due to:");
                Console.WriteLine("   - Missing administrator privileges");
                Console.WriteLine("   - WinDivert driver not loaded");
                Console.WriteLine("   - Antivirus software blocking WinDivert");
            }
            finally
            {
                Console.WriteLine();
                Console.WriteLine("🛑 Stopping network interception...");
                networkCapture.StopCapture();
                networkCapture.Dispose();
                Console.WriteLine("✅ Stopped successfully");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Fatal error: {ex.Message}");
            Console.WriteLine($"Stack trace: {ex.StackTrace}");
        }

        Console.WriteLine();
        Console.WriteLine("Test completed. Press any key to exit...");
        Console.ReadKey();
    }
}
