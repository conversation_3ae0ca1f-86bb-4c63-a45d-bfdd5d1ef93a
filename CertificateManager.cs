using Org.BouncyCastle.Asn1;
using Org.BouncyCastle.Asn1.X509;
using Org.BouncyCastle.Crypto;
using Org.BouncyCastle.Crypto.Generators;
using Org.BouncyCastle.Crypto.Operators;
using Org.BouncyCastle.Crypto.Parameters;
using Org.BouncyCastle.Math;
using Org.BouncyCastle.Security;
using Org.BouncyCastle.X509;
using System.Security.Cryptography.X509Certificates;

namespace NetworkInterceptor;

public class CertificateManager
{
    private readonly string _certificateStorePath;
    private X509Certificate2? _rootCertificate;
    private AsymmetricCipherKeyPair? _rootKeyPair;

    public CertificateManager(string certificateStorePath = "certificates")
    {
        _certificateStorePath = certificateStorePath;
        Directory.CreateDirectory(_certificateStorePath);
        InitializeRootCertificate();
    }

    private void InitializeRootCertificate()
    {
        var rootCertPath = Path.Combine(_certificateStorePath, "root-ca.pfx");
        
        if (File.Exists(rootCertPath))
        {
            try
            {
                _rootCertificate = new X509Certificate2(rootCertPath, "password", X509KeyStorageFlags.Exportable);
                // Extract private key from certificate
                var privateKey = _rootCertificate.GetRSAPrivateKey();
                if (privateKey != null)
                {
                    var parameters = privateKey.ExportParameters(true);
                    var rsaPrivateKey = new RsaPrivateCrtKeyParameters(
                        new BigInteger(1, parameters.Modulus!),
                        new BigInteger(1, parameters.Exponent!),
                        new BigInteger(1, parameters.D!),
                        new BigInteger(1, parameters.P!),
                        new BigInteger(1, parameters.Q!),
                        new BigInteger(1, parameters.DP!),
                        new BigInteger(1, parameters.DQ!),
                        new BigInteger(1, parameters.InverseQ!));
                    
                    var rsaPublicKey = new RsaKeyParameters(false,
                        new BigInteger(1, parameters.Modulus!),
                        new BigInteger(1, parameters.Exponent!));
                    
                    _rootKeyPair = new AsymmetricCipherKeyPair(rsaPublicKey, rsaPrivateKey);
                }
                return;
            }
            catch
            {
                // If loading fails, create new certificate
            }
        }

        CreateRootCertificate();
    }

    private void CreateRootCertificate()
    {
        // Generate key pair
        var keyGenerator = new RsaKeyPairGenerator();
        keyGenerator.Init(new KeyGenerationParameters(new SecureRandom(), 2048));
        _rootKeyPair = keyGenerator.GenerateKeyPair();

        // Create certificate
        var certificateGenerator = new X509V3CertificateGenerator();
        var serialNumber = BigInteger.ProbablePrime(120, new Random());
        
        certificateGenerator.SetSerialNumber(serialNumber);
        certificateGenerator.SetSubjectDN(new X509Name("CN=Network Interceptor Root CA"));
        certificateGenerator.SetIssuerDN(new X509Name("CN=Network Interceptor Root CA"));
        certificateGenerator.SetNotBefore(DateTime.UtcNow.Date);
        certificateGenerator.SetNotAfter(DateTime.UtcNow.Date.AddYears(10));
        certificateGenerator.SetPublicKey(_rootKeyPair.Public);

        // Add extensions
        certificateGenerator.AddExtension(X509Extensions.BasicConstraints, true, new BasicConstraints(true));
        certificateGenerator.AddExtension(X509Extensions.KeyUsage, true, 
            new KeyUsage(KeyUsage.KeyCertSign | KeyUsage.CrlSign));

        // Sign certificate
        var signatureFactory = new Asn1SignatureFactory("SHA256WithRSA", _rootKeyPair.Private, new SecureRandom());
        var certificate = certificateGenerator.Generate(signatureFactory);

        // Convert to .NET certificate
        var certificateBytes = certificate.GetEncoded();
        _rootCertificate = new X509Certificate2(certificateBytes);
        
        // Export with private key
        var pfxBytes = _rootCertificate.Export(X509ContentType.Pfx, "password");
        File.WriteAllBytes(Path.Combine(_certificateStorePath, "root-ca.pfx"), pfxBytes);
        
        // Export public certificate for installation
        var cerBytes = _rootCertificate.Export(X509ContentType.Cert);
        File.WriteAllBytes(Path.Combine(_certificateStorePath, "root-ca.cer"), cerBytes);
    }

    public X509Certificate2 GenerateServerCertificate(string hostname)
    {
        if (_rootKeyPair == null || _rootCertificate == null)
            throw new InvalidOperationException("Root certificate not initialized");

        var certPath = Path.Combine(_certificateStorePath, $"{hostname}.pfx");
        
        if (File.Exists(certPath))
        {
            try
            {
                return new X509Certificate2(certPath, "password", X509KeyStorageFlags.Exportable);
            }
            catch
            {
                // If loading fails, create new certificate
            }
        }

        // Generate new key pair for server certificate
        var keyGenerator = new RsaKeyPairGenerator();
        keyGenerator.Init(new KeyGenerationParameters(new SecureRandom(), 2048));
        var serverKeyPair = keyGenerator.GenerateKeyPair();

        // Create server certificate
        var certificateGenerator = new X509V3CertificateGenerator();
        var serialNumber = BigInteger.ProbablePrime(120, new Random());
        
        certificateGenerator.SetSerialNumber(serialNumber);
        certificateGenerator.SetSubjectDN(new X509Name($"CN={hostname}"));
        certificateGenerator.SetIssuerDN(new X509Name("CN=Network Interceptor Root CA"));
        certificateGenerator.SetNotBefore(DateTime.UtcNow.Date);
        certificateGenerator.SetNotAfter(DateTime.UtcNow.Date.AddYears(1));
        certificateGenerator.SetPublicKey(serverKeyPair.Public);

        // Add extensions
        certificateGenerator.AddExtension(X509Extensions.BasicConstraints, false, new BasicConstraints(false));
        certificateGenerator.AddExtension(X509Extensions.KeyUsage, true, 
            new KeyUsage(KeyUsage.DigitalSignature | KeyUsage.KeyEncipherment));
        
        // Add Subject Alternative Name
        var subjectAltNames = new List<GeneralName>
        {
            new GeneralName(GeneralName.DnsName, hostname)
        };
        certificateGenerator.AddExtension(X509Extensions.SubjectAlternativeName, false, 
            new GeneralNames(subjectAltNames.ToArray()));

        // Sign with root certificate
        var signatureFactory = new Asn1SignatureFactory("SHA256WithRSA", _rootKeyPair.Private, new SecureRandom());
        var certificate = certificateGenerator.Generate(signatureFactory);

        // Convert to .NET certificate
        var certificateBytes = certificate.GetEncoded();
        var serverCert = new X509Certificate2(certificateBytes);
        
        // Export with private key
        var pfxBytes = serverCert.Export(X509ContentType.Pfx, "password");
        File.WriteAllBytes(certPath, pfxBytes);

        return new X509Certificate2(certPath, "password", X509KeyStorageFlags.Exportable);
    }

    public void InstallRootCertificate()
    {
        if (_rootCertificate == null)
            throw new InvalidOperationException("Root certificate not available");

        using var store = new X509Store(StoreName.Root, StoreLocation.CurrentUser);
        store.Open(OpenFlags.ReadWrite);
        
        // Check if certificate is already installed
        var existingCerts = store.Certificates.Find(X509FindType.FindBySubjectName, 
            "Network Interceptor Root CA", false);
        
        if (existingCerts.Count == 0)
        {
            store.Add(_rootCertificate);
            Console.WriteLine("Root certificate installed successfully.");
        }
        else
        {
            Console.WriteLine("Root certificate already installed.");
        }
    }

    public void UninstallRootCertificate()
    {
        using var store = new X509Store(StoreName.Root, StoreLocation.CurrentUser);
        store.Open(OpenFlags.ReadWrite);
        
        var existingCerts = store.Certificates.Find(X509FindType.FindBySubjectName, 
            "Network Interceptor Root CA", false);
        
        foreach (var cert in existingCerts)
        {
            store.Remove(cert);
        }
        
        Console.WriteLine("Root certificate uninstalled.");
    }

    public X509Certificate2? GetRootCertificate() => _rootCertificate;
}
