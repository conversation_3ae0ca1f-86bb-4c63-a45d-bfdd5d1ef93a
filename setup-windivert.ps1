# PowerShell script to download and setup WinDivert
# Run this script as Administrator

param(
    [string]$Version = "2.2.2"
)

Write-Host "=== WinDivert Setup Script ===" -ForegroundColor Green
Write-Host "This script will download and setup WinDivert for the Network Interceptor project." -ForegroundColor Yellow
Write-Host ""

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
if (-not $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "ERROR: This script must be run as Administrator!" -ForegroundColor Red
    Write-Host "Please right-click on PowerShell and select 'Run as Administrator'" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

# Create Native directory if it doesn't exist
$nativeDir = Join-Path $PSScriptRoot "Native"
if (-not (Test-Path $nativeDir)) {
    New-Item -ItemType Directory -Path $nativeDir -Force | Out-Null
    Write-Host "Created Native directory: $nativeDir" -ForegroundColor Green
}

# Download URL for WinDivert
$downloadUrl = "https://github.com/basil00/Divert/releases/download/v$Version/WinDivert-$Version-A.zip"
$zipPath = Join-Path $env:TEMP "WinDivert-$Version-A.zip"
$extractPath = Join-Path $env:TEMP "WinDivert-$Version-A"

try {
    Write-Host "Downloading WinDivert $Version..." -ForegroundColor Yellow
    Write-Host "URL: $downloadUrl" -ForegroundColor Gray
    
    # Download the file
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
    Write-Host "Download completed: $zipPath" -ForegroundColor Green
    
    # Extract the zip file
    Write-Host "Extracting WinDivert..." -ForegroundColor Yellow
    if (Test-Path $extractPath) {
        Remove-Item $extractPath -Recurse -Force
    }
    Expand-Archive -Path $zipPath -DestinationPath $extractPath -Force
    
    # Find the x64 directory
    $x64Dir = Get-ChildItem -Path $extractPath -Recurse -Directory | Where-Object { $_.Name -eq "x64" } | Select-Object -First 1
    
    if ($null -eq $x64Dir) {
        throw "Could not find x64 directory in the extracted files"
    }
    
    Write-Host "Found x64 directory: $($x64Dir.FullName)" -ForegroundColor Green
    
    # Copy required files to Native directory
    $filesToCopy = @("WinDivert.dll", "WinDivert.sys")
    
    foreach ($file in $filesToCopy) {
        $sourcePath = Join-Path $x64Dir.FullName $file
        $destPath = Join-Path $nativeDir $file
        
        if (Test-Path $sourcePath) {
            Copy-Item $sourcePath $destPath -Force
            Write-Host "Copied: $file" -ForegroundColor Green
        } else {
            Write-Host "WARNING: File not found: $file" -ForegroundColor Yellow
        }
    }
    
    # Also copy the lib file if it exists
    $libFile = Join-Path $x64Dir.FullName "WinDivert.lib"
    if (Test-Path $libFile) {
        Copy-Item $libFile (Join-Path $nativeDir "WinDivert.lib") -Force
        Write-Host "Copied: WinDivert.lib" -ForegroundColor Green
    }
    
    Write-Host "" -ForegroundColor Green
    Write-Host "=== Setup Completed Successfully ===" -ForegroundColor Green
    Write-Host "WinDivert files have been copied to: $nativeDir" -ForegroundColor Green
    Write-Host ""
    Write-Host "Files copied:" -ForegroundColor Yellow
    Get-ChildItem $nativeDir | ForEach-Object { Write-Host "  - $($_.Name)" -ForegroundColor White }
    Write-Host ""
    Write-Host "You can now build and run the Network Interceptor project." -ForegroundColor Green
    Write-Host "Remember to run the application as Administrator!" -ForegroundColor Yellow
    
} catch {
    Write-Host "ERROR: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host ""
    Write-Host "Manual download instructions:" -ForegroundColor Yellow
    Write-Host "1. Visit: https://www.reqrypt.org/windivert.html" -ForegroundColor White
    Write-Host "2. Download WinDivert $Version" -ForegroundColor White
    Write-Host "3. Extract the zip file" -ForegroundColor White
    Write-Host "4. Copy the following files from the x64 folder to the Native directory:" -ForegroundColor White
    Write-Host "   - WinDivert.dll" -ForegroundColor White
    Write-Host "   - WinDivert.sys" -ForegroundColor White
    Write-Host "   - WinDivert.lib (optional)" -ForegroundColor White
} finally {
    # Cleanup temporary files
    if (Test-Path $zipPath) {
        Remove-Item $zipPath -Force
    }
    if (Test-Path $extractPath) {
        Remove-Item $extractPath -Recurse -Force
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
