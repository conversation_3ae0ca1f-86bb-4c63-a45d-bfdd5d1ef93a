# Network Interceptor

一个基于 .NET Core 9 的网络流量拦截和修改工具，支持 Windows 进程级别的 UDP/TCP 协议拦截，包括 HTTPS 流量处理。

## 功能特性

- **进程管理**: 获取和选择 Windows 进程进行网络监控
- **协议支持**: 拦截和修改 UDP、TCP 协议数据包
- **HTTPS 支持**: 通过自建证书实现 HTTPS 流量解密和修改
- **实时拦截**: 基于 WinDivert 的高性能网络包拦截
- **规则配置**: 灵活的拦截规则配置系统
- **证书管理**: 自动生成和管理 SSL/TLS 证书

## 系统要求

- Windows 10/11 (x64)
- .NET 9.0 Runtime
- 管理员权限 (必需)
- Visual C++ Redistributable

## 依赖库

### C# NuGet 包
- `SharpPcap` - 跨平台网络包捕获框架
- `PacketDotNet` - 网络包解析和构造
- `BouncyCastle.Cryptography` - 加密和证书管理
- `System.Management` - Windows 系统管理

### 原生库 (需要手动下载)
- **WinDivert** - Windows 网络包拦截驱动

## 安装步骤

### 1. 下载 WinDivert

1. 访问 [WinDivert 官网](https://www.reqrypt.org/windivert.html)
2. 下载最新版本的 WinDivert (推荐 2.2.0+)
3. 解压下载的文件
4. 将以下文件复制到项目的 `Native` 目录:
   ```
   Native/
   ├── WinDivert.dll (x64版本)
   ├── WinDivert.sys
   └── WinDivert.lib (可选)
   ```

### 2. 编译项目

```bash
# 克隆或下载项目
cd NetworkInterceptor

# 还原 NuGet 包
dotnet restore

# 编译项目
dotnet build -c Release

# 运行项目 (需要管理员权限)
dotnet run
```

## 使用说明

### 启动应用

**重要**: 必须以管理员身份运行，否则无法进行网络拦截。

```bash
# 以管理员身份打开命令提示符或 PowerShell
dotnet run
```

### 基本操作流程

1. **选择目标进程**
   - 使用菜单选项 1 查看所有进程
   - 使用菜单选项 2 选择要监控的进程

2. **配置拦截规则** (可选)
   - 使用菜单选项 4 配置具体的拦截规则
   - 可以设置协议过滤、端口过滤等

3. **安装证书** (HTTPS 支持)
   - 使用菜单选项 7 安装根证书
   - 这是拦截 HTTPS 流量的必要步骤

4. **开始拦截**
   - 使用菜单选项 5 开始网络拦截
   - 实时查看拦截到的数据包

5. **停止拦截**
   - 使用菜单选项 6 停止拦截

### 高级功能

#### HTTPS 流量拦截

1. 首先安装根证书:
   ```
   选择菜单 7 -> 选择 1 (安装根证书)
   ```

2. 为目标域名生成服务器证书:
   ```
   选择菜单 7 -> 选择 2 (生成服务器证书)
   输入目标域名，如: www.example.com
   ```

3. 配置应用程序使用代理 (需要额外实现)

#### 数据包修改

通过配置拦截规则，可以实现:
- 替换特定的文本内容
- 修改 HTTP 头部
- 重定向请求
- 阻止特定连接

## 项目结构

```
NetworkInterceptor/
├── Models/
│   └── ProcessInfo.cs          # 数据模型定义
├── Native/                     # 原生库文件 (需要手动添加)
│   ├── WinDivert.dll
│   └── WinDivert.sys
├── certificates/               # 证书存储目录 (自动创建)
├── NetworkCapture.cs           # 网络拦截核心类
├── ProcessManager.cs           # 进程管理类
├── WinDivertWrapper.cs         # WinDivert P/Invoke 包装
├── CertificateManager.cs       # 证书管理类
├── Program.cs                  # 主程序入口
├── NetworkInterceptor.csproj   # 项目文件
└── README.md                   # 说明文档
```

## 技术实现

### 网络拦截原理

1. **WinDivert 驱动**: 在内核层拦截网络包
2. **包解析**: 使用 PacketDotNet 解析协议头部
3. **进程关联**: 通过端口映射关联到具体进程
4. **内容修改**: 修改包内容并重新计算校验和
5. **包转发**: 将处理后的包发送回网络栈

### HTTPS 处理流程

1. **根证书生成**: 使用 BouncyCastle 生成自签名根证书
2. **服务器证书**: 为每个域名动态生成服务器证书
3. **证书安装**: 将根证书安装到系统信任存储
4. **TLS 终止**: 在代理层终止 TLS 连接
5. **内容解密**: 获取明文 HTTP 内容进行处理

## 注意事项

### 安全警告

- 此工具仅用于合法的网络分析和调试目的
- 请勿用于恶意攻击或未授权的网络监听
- 使用前请确保符合当地法律法规

### 技术限制

- 需要管理员权限运行
- 仅支持 Windows 平台
- 某些防病毒软件可能会误报
- 高流量环境下可能影响性能

### 故障排除

1. **"Failed to open WinDivert handle"**
   - 确保以管理员身份运行
   - 检查 WinDivert.dll 和 WinDivert.sys 是否存在
   - 确认系统支持 WinDivert

2. **证书相关错误**
   - 确保有足够的权限访问证书存储
   - 检查证书文件是否损坏

3. **进程无法选择**
   - 某些系统进程可能无法访问
   - 尝试选择用户级别的进程

## 开发扩展

### 添加新的协议支持

1. 在 `NetworkCapture.cs` 中添加协议解析逻辑
2. 扩展 `InterceptedPacket` 模型
3. 实现对应的修改规则

### 添加 GUI 界面

可以使用以下技术栈:
- WPF (Windows Presentation Foundation)
- WinUI 3
- Avalonia (跨平台)

### 性能优化

- 使用异步 I/O 处理大量数据包
- 实现包缓冲和批处理
- 添加多线程处理支持

## 许可证

本项目仅供学习和研究使用。使用时请遵守相关法律法规。

## 贡献

欢迎提交 Issue 和 Pull Request 来改进这个项目。

## 联系方式

如有问题或建议，请通过 GitHub Issues 联系。
