# Network Capture Verification Script
Write-Host "=== Network Capture Verification ===" -ForegroundColor Green
Write-Host ""

# Check admin privileges
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

Write-Host "Administrator privileges: $(if ($isAdmin) { '✅ Yes' } else { '❌ No' })"

# Check WinDivert files in output directory
$outputDir = "bin\Debug\net9.0\win-x64"
$winDivertDll = Join-Path $outputDir "Native\WinDivert.dll"
$winDivertSys = Join-Path $outputDir "Native\WinDivert.sys"
$quickTestExe = Join-Path $outputDir "QuickTest.exe"

Write-Host ""
Write-Host "Checking files in output directory:" -ForegroundColor Yellow
Write-Host "Output directory: $outputDir"
Write-Host "QuickTest.exe: $(if (Test-Path $quickTestExe) { '✅ Found' } else { '❌ Missing' })"
Write-Host "WinDivert.dll: $(if (Test-Path $winDivertDll) { '✅ Found' } else { '❌ Missing' })"
Write-Host "WinDivert.sys: $(if (Test-Path $winDivertSys) { '✅ Found' } else { '❌ Missing' })"

Write-Host ""
if ($isAdmin -and (Test-Path $quickTestExe) -and (Test-Path $winDivertDll) -and (Test-Path $winDivertSys)) {
    Write-Host "🚀 All requirements met! Running network capture test..." -ForegroundColor Green
    Write-Host ""
    
    try {
        # Change to output directory and run test
        Push-Location $outputDir
        .\QuickTest.exe
        Pop-Location
    }
    catch {
        Write-Host "❌ Error running test: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "❌ Requirements not met:" -ForegroundColor Red
    if (-not $isAdmin) {
        Write-Host "   - Need administrator privileges" -ForegroundColor Yellow
    }
    if (-not (Test-Path $quickTestExe)) {
        Write-Host "   - QuickTest.exe not found (run: dotnet build QuickTest.csproj)" -ForegroundColor Yellow
    }
    if (-not (Test-Path $winDivertDll) -or -not (Test-Path $winDivertSys)) {
        Write-Host "   - WinDivert files missing in output directory" -ForegroundColor Yellow
    }
}

Write-Host ""
Read-Host "Press Enter to exit"
