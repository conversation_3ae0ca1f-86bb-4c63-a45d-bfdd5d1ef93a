using System.Diagnostics;
using System.Security.Principal;
using System.Runtime.InteropServices;

class SimpleNetworkTest
{
    // Simple WinDivert P/Invoke declarations
    [DllImport("WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
    static extern IntPtr WinDivertOpen(string filter, int layer, short priority, ulong flags);

    [DllImport("WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
    static extern bool WinDivertClose(IntPtr handle);

    [DllImport("WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
    static extern bool WinDivertRecv(IntPtr handle, byte[] packet, uint packetLen, out uint readLen, IntPtr addr);

    [DllImport("WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
    static extern bool WinDivertSend(IntPtr handle, byte[] packet, uint readLen, out uint writeLen, IntPtr addr);

    static void Main(string[] args)
    {
        Console.WriteLine("=== Simple Network Test ===");
        Console.WriteLine("Testing basic network packet capture");
        Console.WriteLine();

        // Check admin privileges
        var isAdmin = IsRunningAsAdministrator();
        Console.WriteLine($"Administrator privileges: {(isAdmin ? "✅ Yes" : "❌ No")}");
        
        if (!isAdmin)
        {
            Console.WriteLine("❌ This test requires administrator privileges.");
            Console.WriteLine("Please run as administrator.");
            Console.ReadKey();
            return;
        }

        // Check WinDivert files
        var winDivertDll = Path.Combine("Native", "WinDivert.dll");
        var winDivertSys = Path.Combine("Native", "WinDivert.sys");
        
        Console.WriteLine($"WinDivert.dll: {(File.Exists(winDivertDll) ? "✅ Found" : "❌ Missing")}");
        Console.WriteLine($"WinDivert.sys: {(File.Exists(winDivertSys) ? "✅ Found" : "❌ Missing")}");
        
        if (!File.Exists(winDivertDll) || !File.Exists(winDivertSys))
        {
            Console.WriteLine("❌ WinDivert files are missing.");
            Console.ReadKey();
            return;
        }

        Console.WriteLine();
        Console.WriteLine("🚀 Starting basic packet capture test...");
        Console.WriteLine("   This will capture HTTP traffic on port 80");
        Console.WriteLine("   Press any key to stop");
        Console.WriteLine();

        IntPtr handle = IntPtr.Zero;
        try
        {
            // Open WinDivert handle for HTTP traffic (port 80)
            string filter = "tcp.DstPort == 80 or tcp.SrcPort == 80";
            handle = WinDivertOpen(filter, 0, 0, 0);

            if (handle == IntPtr.Zero)
            {
                Console.WriteLine("❌ Failed to open WinDivert handle");
                Console.WriteLine("   Possible causes:");
                Console.WriteLine("   - Not running as administrator");
                Console.WriteLine("   - WinDivert driver not loaded");
                Console.WriteLine("   - Antivirus blocking WinDivert");
                return;
            }

            Console.WriteLine("✅ WinDivert handle opened successfully");
            Console.WriteLine("📡 Capturing packets... (open a website in your browser)");
            Console.WriteLine();

            var buffer = new byte[65535];
            int packetCount = 0;
            var startTime = DateTime.Now;

            // Start a background task to check for key press
            var cancellationToken = new CancellationTokenSource();
            var keyTask = Task.Run(() =>
            {
                Console.ReadKey();
                cancellationToken.Cancel();
            });

            while (!cancellationToken.Token.IsCancellationRequested)
            {
                if (WinDivertRecv(handle, buffer, (uint)buffer.Length, out uint readLen, IntPtr.Zero))
                {
                    packetCount++;
                    var now = DateTime.Now;
                    
                    // Parse basic IP header to get source and destination
                    if (readLen >= 20) // Minimum IP header size
                    {
                        var srcIP = $"{buffer[12]}.{buffer[13]}.{buffer[14]}.{buffer[15]}";
                        var dstIP = $"{buffer[16]}.{buffer[17]}.{buffer[18]}.{buffer[19]}";
                        
                        Console.WriteLine($"[{now:HH:mm:ss.fff}] Packet #{packetCount}: {srcIP} -> {dstIP} ({readLen} bytes)");
                        
                        // Look for HTTP data
                        var packetData = System.Text.Encoding.ASCII.GetString(buffer, 0, (int)readLen);
                        if (packetData.Contains("HTTP/") || packetData.Contains("GET ") || packetData.Contains("POST "))
                        {
                            Console.WriteLine($"   🌐 HTTP traffic detected!");
                            var lines = packetData.Split('\n');
                            foreach (var line in lines.Take(3))
                            {
                                if (!string.IsNullOrWhiteSpace(line))
                                    Console.WriteLine($"   📄 {line.Trim()}");
                            }
                        }
                    }

                    // Forward the packet (important!)
                    WinDivertSend(handle, buffer, readLen, out _, IntPtr.Zero);

                    // Limit output to prevent spam
                    if (packetCount >= 20)
                    {
                        Console.WriteLine("   ... (limiting output, press any key to stop)");
                        break;
                    }
                }
                else
                {
                    // Small delay to prevent busy waiting
                    Thread.Sleep(1);
                }
            }

            var duration = DateTime.Now - startTime;
            Console.WriteLine();
            Console.WriteLine($"📊 Capture completed:");
            Console.WriteLine($"   Total packets: {packetCount}");
            Console.WriteLine($"   Duration: {duration.TotalSeconds:F1} seconds");
            Console.WriteLine($"   Rate: {(packetCount / duration.TotalSeconds):F1} packets/sec");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"❌ Error: {ex.Message}");
            Console.WriteLine($"   Stack trace: {ex.StackTrace}");
        }
        finally
        {
            if (handle != IntPtr.Zero)
            {
                WinDivertClose(handle);
                Console.WriteLine("✅ WinDivert handle closed");
            }
        }

        Console.WriteLine();
        Console.WriteLine("Test completed. Press any key to exit...");
        Console.ReadKey();
    }

    static bool IsRunningAsAdministrator()
    {
        try
        {
            var identity = WindowsIdentity.GetCurrent();
            var principal = new WindowsPrincipal(identity);
            return principal.IsInRole(WindowsBuiltInRole.Administrator);
        }
        catch
        {
            return false;
        }
    }
}
