using System.Runtime.InteropServices;

namespace NetworkInterceptor;

public static class WinDivertWrapper
{
    private const string WinDivertDll = "WinDivert.dll";

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern IntPtr WinDivertOpen(string filter, WinDivertLayer layer, short priority, WinDivertFlags flags);

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern bool WinDivertClose(IntPtr handle);

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern bool WinDivertRecv(IntPtr handle, byte[] packet, uint packetLen, out uint readLen, out WinDivertAddress addr);

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern bool WinDivertSend(IntPtr handle, byte[] packet, uint packetLen, out uint writeLen, ref WinDivertAddress addr);

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern bool WinDivertHelperParsePacket(byte[] packet, uint packetLen, out WinDivertIpHeader ipHeader, 
        out WinDivertIpv6Header ipv6Header, out WinDivertIcmpHeader icmpHeader, out WinDivertIcmpv6Header icmpv6Header,
        out WinDivertTcpHeader tcpHeader, out WinDivertUdpHeader udpHeader, out IntPtr payload, out uint payloadLen);

    [DllImport(WinDivertDll, CallingConvention = CallingConvention.Cdecl)]
    public static extern uint WinDivertHelperCalcChecksums(byte[] packet, uint packetLen, ref WinDivertAddress addr, WinDivertChecksumFlags flags);

    public enum WinDivertLayer
    {
        Network = 0,
        NetworkForward = 1,
        Flow = 2,
        Socket = 3,
        Reflect = 4
    }

    [Flags]
    public enum WinDivertFlags : ulong
    {
        Default = 0,
        Sniff = 1,
        Drop = 2,
        RecvOnly = 4,
        SendOnly = 8,
        NoInstall = 16,
        Fragments = 32
    }

    [Flags]
    public enum WinDivertChecksumFlags : ulong
    {
        Default = 0,
        NoIpChecksum = 1,
        NoIcmpChecksum = 2,
        NoIcmpv6Checksum = 4,
        NoTcpChecksum = 8,
        NoUdpChecksum = 16
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertAddress
    {
        public long Timestamp;
        public uint IfIdx;
        public uint SubIfIdx;
        public byte Direction;
        public byte Loopback;
        public byte Impostor;
        public byte IPv6;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertIpHeader
    {
        public byte HdrLength;
        public byte Version;
        public byte TOS;
        public ushort Length;
        public ushort Id;
        public ushort FragOff0;
        public byte TTL;
        public byte Protocol;
        public ushort Checksum;
        public uint SrcAddr;
        public uint DstAddr;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertIpv6Header
    {
        public uint TrafficClass0;
        public uint TrafficClass1;
        public uint FlowLabel0;
        public uint FlowLabel1;
        public ushort Length;
        public byte NextHdr;
        public byte HopLimit;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
        public byte[] SrcAddr;
        [MarshalAs(UnmanagedType.ByValArray, SizeConst = 16)]
        public byte[] DstAddr;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertTcpHeader
    {
        public ushort SrcPort;
        public ushort DstPort;
        public uint SeqNum;
        public uint AckNum;
        public byte Reserved1;
        public byte Reserved2;
        public ushort Window;
        public ushort Checksum;
        public ushort UrgPtr;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertUdpHeader
    {
        public ushort SrcPort;
        public ushort DstPort;
        public ushort Length;
        public ushort Checksum;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertIcmpHeader
    {
        public byte Type;
        public byte Code;
        public ushort Checksum;
        public uint Body;
    }

    [StructLayout(LayoutKind.Sequential)]
    public struct WinDivertIcmpv6Header
    {
        public byte Type;
        public byte Code;
        public ushort Checksum;
        public uint Body;
    }
}
