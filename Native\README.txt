WinDivert Native Libraries Directory
=====================================

This directory should contain the following WinDivert files:

Required files:
- WinDivert.dll (x64 version)
- WinDivert.sys (kernel driver)

Optional files:
- WinDivert.lib (for native development)

How to obtain these files:
=========================

Option 1: Run the setup script (Recommended)
--------------------------------------------
1. Right-click on "setup-windivert.bat" and select "Run as administrator"
2. The script will automatically download and extract the required files

Option 2: Manual download
-------------------------
1. Visit: https://www.reqrypt.org/windivert.html
2. Download the latest WinDivert release (2.2.0 or newer)
3. Extract the downloaded zip file
4. Copy the files from the "x64" folder to this directory:
   - WinDivert.dll
   - WinDivert.sys
   - WinDivert.lib (optional)

Important Notes:
===============
- Make sure to use the x64 (64-bit) versions of the files
- The application requires administrator privileges to load the WinDivert driver
- Some antivirus software may flag WinDivert as potentially unwanted software
- WinDivert is a legitimate network analysis tool used by many security applications

Troubleshooting:
===============
If you encounter issues:
1. Ensure you're running as administrator
2. Check that Windows Defender or antivirus isn't blocking the files
3. Verify the files are the correct x64 versions
4. Make sure your system supports WinDivert (Windows 7+ required)

For more information about WinDivert, visit:
https://www.reqrypt.org/windivert.html
