{"NetworkInterceptor": {"CertificateSettings": {"StorePath": "certificates", "RootCertificatePassword": "password", "CertificateValidityYears": 10, "ServerCertificateValidityYears": 1}, "CaptureSettings": {"BufferSize": 65535, "MaxPacketsPerSecond": 10000, "EnablePacketLogging": true, "LogFilePath": "logs/packets.log"}, "FilterSettings": {"DefaultProtocols": ["TCP", "UDP"], "ExcludedPorts": [135, 139, 445, 1900], "ExcludedProcesses": ["System", "svchost", "winlogon"]}, "ProxySettings": {"HttpProxyPort": 8080, "HttpsProxyPort": 8443, "EnableTransparentProxy": false}}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft": "Warning", "Microsoft.Hosting.Lifetime": "Information"}, "Console": {"IncludeScopes": false, "TimestampFormat": "yyyy-MM-dd HH:mm:ss "}}}