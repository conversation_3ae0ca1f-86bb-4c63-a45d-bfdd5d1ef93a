# PowerShell script to test the build and basic functionality
Write-Host "=== Network Interceptor Build Test ===" -ForegroundColor Green
Write-Host ""

# Test 1: Check if .NET 9 is available
Write-Host "Testing .NET 9 availability..." -ForegroundColor Yellow
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET version: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET is not installed or not in PATH" -ForegroundColor Red
    exit 1
}

# Test 2: Restore packages
Write-Host "`nRestoring NuGet packages..." -ForegroundColor Yellow
$restoreResult = dotnet restore
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Package restore successful" -ForegroundColor Green
} else {
    Write-Host "✗ Package restore failed" -ForegroundColor Red
    exit 1
}

# Test 3: Build project
Write-Host "`nBuilding project..." -ForegroundColor Yellow
$buildResult = dotnet build -c Release
if ($LASTEXITCODE -eq 0) {
    Write-Host "✓ Build successful" -ForegroundColor Green
} else {
    Write-Host "✗ Build failed" -ForegroundColor Red
    exit 1
}

# Test 4: Check if WinDivert files exist
Write-Host "`nChecking WinDivert dependencies..." -ForegroundColor Yellow
$nativeDir = "Native"
$requiredFiles = @("WinDivert.dll", "WinDivert.sys")

foreach ($file in $requiredFiles) {
    $filePath = Join-Path $nativeDir $file
    if (Test-Path $filePath) {
        Write-Host "✓ Found: $file" -ForegroundColor Green
    } else {
        Write-Host "⚠ Missing: $file (required for network interception)" -ForegroundColor Yellow
    }
}

# Test 5: Check administrator privileges
Write-Host "`nChecking administrator privileges..." -ForegroundColor Yellow
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
if ($currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)) {
    Write-Host "✓ Running as Administrator" -ForegroundColor Green
    $canRunInterception = $true
} else {
    Write-Host "⚠ Not running as Administrator (required for network interception)" -ForegroundColor Yellow
    $canRunInterception = $false
}

# Test 6: Basic functionality test (without admin privileges)
Write-Host "`nTesting basic functionality..." -ForegroundColor Yellow
try {
    # This would test basic class instantiation without network operations
    Write-Host "✓ Basic functionality test would go here" -ForegroundColor Green
} catch {
    Write-Host "✗ Basic functionality test failed" -ForegroundColor Red
}

# Summary
Write-Host "`n=== Test Summary ===" -ForegroundColor Green
Write-Host "Build Status: ✓ Success" -ForegroundColor Green

if (-not (Test-Path (Join-Path $nativeDir "WinDivert.dll"))) {
    Write-Host "Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Run 'setup-windivert.bat' as Administrator to download WinDivert" -ForegroundColor White
    Write-Host "2. Then run the application as Administrator" -ForegroundColor White
} elseif (-not $canRunInterception) {
    Write-Host "Next Steps:" -ForegroundColor Yellow
    Write-Host "1. Run the application as Administrator to enable network interception" -ForegroundColor White
} else {
    Write-Host "✓ Ready to run! Execute: dotnet run" -ForegroundColor Green
}

Write-Host "`nPress any key to exit..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
