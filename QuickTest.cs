using System.Diagnostics;
using System.Security.Principal;
using System.Runtime.InteropServices;

namespace QuickTest
{
    class Program
    {
        // Simple WinDivert P/Invoke declarations
        [DllImport("Native/WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern IntPtr WinDivertOpen(string filter, int layer, short priority, ulong flags);

        [DllImport("Native/WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool WinDivertClose(IntPtr handle);

        [DllImport("Native/WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool WinDivertRecv(IntPtr handle, byte[] packet, uint packetLen, out uint readLen, IntPtr addr);

        [DllImport("Native/WinDivert.dll", CallingConvention = CallingConvention.Cdecl)]
        static extern bool WinDivertSend(IntPtr handle, byte[] packet, uint readLen, out uint writeLen, IntPtr addr);

        static void Main(string[] args)
        {
            Console.WriteLine("=== Quick Network Capture Test ===");
            Console.WriteLine();

            // Check admin privileges
            var isAdmin = IsRunningAsAdministrator();
            Console.WriteLine($"Administrator privileges: {(isAdmin ? "✅ Yes" : "❌ No")}");
            
            if (!isAdmin)
            {
                Console.WriteLine("❌ This test requires administrator privileges.");
                Console.WriteLine("Please run as administrator.");
                Console.ReadKey();
                return;
            }

            // Check WinDivert files
            var winDivertDll = Path.Combine("Native", "WinDivert.dll");
            Console.WriteLine($"WinDivert.dll: {(File.Exists(winDivertDll) ? "✅ Found" : "❌ Missing")}");
            
            if (!File.Exists(winDivertDll))
            {
                Console.WriteLine("❌ WinDivert.dll not found in Native directory");
                Console.ReadKey();
                return;
            }

            Console.WriteLine();
            Console.WriteLine("🚀 Testing WinDivert basic functionality...");

            IntPtr handle = IntPtr.Zero;
            try
            {
                // Try to open WinDivert with a simple filter
                string filter = "tcp.DstPort == 80 or tcp.SrcPort == 80 or tcp.DstPort == 443 or tcp.SrcPort == 443";
                Console.WriteLine($"Filter: {filter}");
                
                handle = WinDivertOpen(filter, 0, 0, 0);

                if (handle == IntPtr.Zero)
                {
                    var error = Marshal.GetLastWin32Error();
                    Console.WriteLine($"❌ Failed to open WinDivert handle (Error: {error})");
                    Console.WriteLine("   Possible causes:");
                    Console.WriteLine("   - Not running as administrator");
                    Console.WriteLine("   - WinDivert driver not loaded");
                    Console.WriteLine("   - Antivirus blocking WinDivert");
                    return;
                }

                Console.WriteLine("✅ WinDivert handle opened successfully!");
                Console.WriteLine("📡 Ready to capture packets...");
                Console.WriteLine();
                Console.WriteLine("Now open Chrome and visit http://example.com or any website");
                Console.WriteLine("Press any key to start capturing (will capture for 10 seconds)");
                Console.ReadKey();

                Console.WriteLine();
                Console.WriteLine("🔍 Capturing packets...");

                var buffer = new byte[65535];
                int packetCount = 0;
                var startTime = DateTime.Now;
                var endTime = startTime.AddSeconds(10);

                while (DateTime.Now < endTime && packetCount < 50)
                {
                    if (WinDivertRecv(handle, buffer, (uint)buffer.Length, out uint readLen, IntPtr.Zero))
                    {
                        packetCount++;
                        var now = DateTime.Now;
                        
                        Console.WriteLine($"[{now:HH:mm:ss.fff}] 📦 Packet #{packetCount} captured ({readLen} bytes)");
                        
                        // Try to parse basic info
                        if (readLen >= 20)
                        {
                            try
                            {
                                // Basic IP header parsing
                                var version = (buffer[0] >> 4) & 0xF;
                                var protocol = buffer[9];
                                var srcIP = $"{buffer[12]}.{buffer[13]}.{buffer[14]}.{buffer[15]}";
                                var dstIP = $"{buffer[16]}.{buffer[17]}.{buffer[18]}.{buffer[19]}";
                                
                                Console.WriteLine($"   🌐 IPv{version} {GetProtocolName(protocol)}: {srcIP} -> {dstIP}");
                                
                                // Check for HTTP content
                                var content = System.Text.Encoding.ASCII.GetString(buffer, 0, Math.Min(200, (int)readLen));
                                if (content.Contains("HTTP/") || content.Contains("GET ") || content.Contains("POST "))
                                {
                                    Console.WriteLine($"   🔍 HTTP content detected!");
                                    var firstLine = content.Split('\r', '\n')[0];
                                    if (!string.IsNullOrWhiteSpace(firstLine))
                                        Console.WriteLine($"   📄 {firstLine}");
                                }
                            }
                            catch (Exception ex)
                            {
                                Console.WriteLine($"   ⚠️ Parse error: {ex.Message}");
                            }
                        }

                        // IMPORTANT: Forward the packet to maintain connectivity
                        WinDivertSend(handle, buffer, readLen, out _, IntPtr.Zero);
                    }
                    else
                    {
                        Thread.Sleep(1);
                    }
                }

                var duration = DateTime.Now - startTime;
                Console.WriteLine();
                Console.WriteLine($"📊 Capture Summary:");
                Console.WriteLine($"   ✅ Successfully captured {packetCount} packets");
                Console.WriteLine($"   ⏱️ Duration: {duration.TotalSeconds:F1} seconds");
                if (packetCount > 0)
                {
                    Console.WriteLine($"   📈 Rate: {(packetCount / duration.TotalSeconds):F1} packets/sec");
                    Console.WriteLine();
                    Console.WriteLine("🎉 SUCCESS! Network interception is working!");
                }
                else
                {
                    Console.WriteLine("   ℹ️ No packets captured. Try browsing to a website.");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Error: {ex.Message}");
            }
            finally
            {
                if (handle != IntPtr.Zero)
                {
                    WinDivertClose(handle);
                    Console.WriteLine("✅ WinDivert handle closed");
                }
            }

            Console.WriteLine();
            Console.WriteLine("Test completed. Press any key to exit...");
            Console.ReadKey();
        }

        static string GetProtocolName(byte protocol)
        {
            return protocol switch
            {
                1 => "ICMP",
                6 => "TCP",
                17 => "UDP",
                _ => $"Protocol-{protocol}"
            };
        }

        static bool IsRunningAsAdministrator()
        {
            try
            {
                var identity = WindowsIdentity.GetCurrent();
                var principal = new WindowsPrincipal(identity);
                return principal.IsInRole(WindowsBuiltInRole.Administrator);
            }
            catch
            {
                return false;
            }
        }
    }
}
