# Simple WinDivert download script
$Version = "2.2.2"
$downloadUrl = "https://github.com/basil00/Divert/releases/download/v$Version/WinDivert-$Version-A.zip"
$zipPath = "WinDivert.zip"
$nativeDir = "Native"

Write-Host "Downloading WinDivert $Version..." -ForegroundColor Yellow

try {
    # Create Native directory
    if (-not (Test-Path $nativeDir)) {
        New-Item -ItemType Directory -Path $nativeDir -Force | Out-Null
    }

    # Download
    Invoke-WebRequest -Uri $downloadUrl -OutFile $zipPath -UseBasicParsing
    Write-Host "Downloaded successfully" -ForegroundColor Green

    # Extract
    Expand-Archive -Path $zipPath -DestinationPath "temp" -Force
    
    # Find x64 files and copy
    $x64Files = Get-ChildItem -Path "temp" -Recurse -Filter "*.dll" | Where-Object { $_.Directory.Name -eq "x64" }
    $x64Files += Get-ChildItem -Path "temp" -Recurse -Filter "*.sys" | Where-Object { $_.Directory.Name -eq "x64" }
    
    foreach ($file in $x64Files) {
        Copy-Item $file.FullName -Destination $nativeDir -Force
        Write-Host "Copied: $($file.Name)" -ForegroundColor Green
    }
    
    # Cleanup
    Remove-Item $zipPath -Force
    Remove-Item "temp" -Recurse -Force
    
    Write-Host "WinDivert setup completed!" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
}
