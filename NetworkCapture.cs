using NetworkInterceptor.Models;
using PacketDotNet;
using System.Net;
using System.Text;

namespace NetworkInterceptor;

public class NetworkCapture : IDisposable
{
    private readonly List<InterceptionRule> _rules = new();
    private readonly Dictionary<int, ProcessInfo> _targetProcesses = new();
    private IntPtr _winDivertHandle = IntPtr.Zero;
    private bool _isCapturing = false;
    private Thread? _captureThread;
    private readonly object _lockObject = new();

    public event EventHandler<InterceptedPacket>? PacketIntercepted;
    public event EventHandler<string>? ErrorOccurred;

    public bool IsCapturing => _isCapturing;

    public void AddInterceptionRule(InterceptionRule rule)
    {
        lock (_lockObject)
        {
            _rules.Add(rule);
        }
    }

    public void RemoveInterceptionRule(InterceptionRule rule)
    {
        lock (_lockObject)
        {
            _rules.Remove(rule);
        }
    }

    public void AddTargetProcess(ProcessInfo process)
    {
        lock (_lockObject)
        {
            _targetProcesses[process.ProcessId] = process;
        }
    }

    public void RemoveTargetProcess(int processId)
    {
        lock (_lockObject)
        {
            _targetProcesses.Remove(processId);
        }
    }

    public void StartCapture()
    {
        if (_isCapturing)
            return;

        try
        {
            // Create WinDivert filter based on target processes
            var filter = BuildWinDivertFilter();
            
            _winDivertHandle = WinDivertWrapper.WinDivertOpen(filter, 
                WinDivertWrapper.WinDivertLayer.Network, 0, 0);

            if (_winDivertHandle == IntPtr.Zero)
            {
                throw new InvalidOperationException("Failed to open WinDivert handle. Make sure you're running as administrator.");
            }

            _isCapturing = true;
            _captureThread = new Thread(CaptureLoop)
            {
                IsBackground = true,
                Name = "NetworkCaptureThread"
            };
            _captureThread.Start();

            Console.WriteLine("Network capture started.");
        }
        catch (Exception ex)
        {
            ErrorOccurred?.Invoke(this, $"Failed to start capture: {ex.Message}");
            throw;
        }
    }

    public void StopCapture()
    {
        if (!_isCapturing)
            return;

        _isCapturing = false;

        if (_winDivertHandle != IntPtr.Zero)
        {
            WinDivertWrapper.WinDivertClose(_winDivertHandle);
            _winDivertHandle = IntPtr.Zero;
        }

        _captureThread?.Join(5000);
        Console.WriteLine("Network capture stopped.");
    }

    private string BuildWinDivertFilter()
    {
        if (_targetProcesses.Count == 0)
            return "false"; // Don't capture anything if no processes selected

        // Build filter to capture packets for specific processes
        // Note: WinDivert doesn't directly support process filtering at network layer
        // This is a simplified approach - in practice, you might need to use socket layer
        var filters = new List<string>();
        
        foreach (var process in _targetProcesses.Values)
        {
            foreach (var connection in process.Connections)
            {
                if (connection.Protocol.ToUpper() == "TCP")
                {
                    filters.Add($"tcp.SrcPort == {connection.LocalPort} or tcp.DstPort == {connection.LocalPort}");
                }
                else if (connection.Protocol.ToUpper() == "UDP")
                {
                    filters.Add($"udp.SrcPort == {connection.LocalPort} or udp.DstPort == {connection.LocalPort}");
                }
            }
        }

        return filters.Count > 0 ? string.Join(" or ", filters) : "tcp or udp";
    }

    private void CaptureLoop()
    {
        var buffer = new byte[65535];

        while (_isCapturing)
        {
            try
            {
                if (WinDivertWrapper.WinDivertRecv(_winDivertHandle, buffer, (uint)buffer.Length, 
                    out uint readLen, out var addr))
                {
                    var packetData = new byte[readLen];
                    Array.Copy(buffer, packetData, readLen);

                    var interceptedPacket = ProcessPacket(packetData, addr);
                    if (interceptedPacket != null)
                    {
                        // Apply rules and modifications
                        var modifiedPacket = ApplyRules(interceptedPacket, packetData);
                        
                        PacketIntercepted?.Invoke(this, interceptedPacket);

                        // Send packet back (modified or original)
                        WinDivertWrapper.WinDivertSend(_winDivertHandle, modifiedPacket, (uint)modifiedPacket.Length, 
                            out _, ref addr);
                    }
                    else
                    {
                        // Forward packet unchanged
                        WinDivertWrapper.WinDivertSend(_winDivertHandle, packetData, readLen, out _, ref addr);
                    }
                }
                else
                {
                    // Check if we should continue or if there was an error
                    if (_isCapturing)
                    {
                        Thread.Sleep(1);
                    }
                }
            }
            catch (Exception ex)
            {
                ErrorOccurred?.Invoke(this, $"Error in capture loop: {ex.Message}");
                Thread.Sleep(100);
            }
        }
    }

    private InterceptedPacket? ProcessPacket(byte[] packetData, WinDivertWrapper.WinDivertAddress addr)
    {
        try
        {
            var packet = Packet.ParsePacket(LinkLayers.Raw, packetData);
            var ipPacket = packet.Extract<IPPacket>();
            
            if (ipPacket == null)
                return null;

            var interceptedPacket = new InterceptedPacket
            {
                Timestamp = DateTime.Now,
                SourceAddress = ipPacket.SourceAddress.ToString(),
                DestinationAddress = ipPacket.DestinationAddress.ToString(),
                IsInbound = addr.Direction == 1,
                Data = packetData
            };

            // Extract protocol-specific information
            var tcpPacket = packet.Extract<TcpPacket>();
            var udpPacket = packet.Extract<UdpPacket>();

            if (tcpPacket != null)
            {
                interceptedPacket.Protocol = "TCP";
                interceptedPacket.SourcePort = tcpPacket.SourcePort;
                interceptedPacket.DestinationPort = tcpPacket.DestinationPort;
            }
            else if (udpPacket != null)
            {
                interceptedPacket.Protocol = "UDP";
                interceptedPacket.SourcePort = udpPacket.SourcePort;
                interceptedPacket.DestinationPort = udpPacket.DestinationPort;
            }
            else
            {
                interceptedPacket.Protocol = ipPacket.Protocol.ToString();
            }

            return interceptedPacket;
        }
        catch
        {
            return null;
        }
    }

    private byte[] ApplyRules(InterceptedPacket interceptedPacket, byte[] originalData)
    {
        lock (_lockObject)
        {
            foreach (var rule in _rules)
            {
                if (ShouldApplyRule(rule, interceptedPacket))
                {
                    if (rule.ModifyContent && !string.IsNullOrEmpty(rule.ModificationPattern))
                    {
                        return ModifyPacketContent(originalData, rule);
                    }
                }
            }
        }

        return originalData;
    }

    private bool ShouldApplyRule(InterceptionRule rule, InterceptedPacket packet)
    {
        // Check process ID (if we had process mapping)
        if (rule.ProcessId != 0 && packet.ProcessId != rule.ProcessId)
            return false;

        // Check protocol
        if (!string.IsNullOrEmpty(rule.Protocol) && 
            !rule.Protocol.Equals(packet.Protocol, StringComparison.OrdinalIgnoreCase))
            return false;

        // Check target address
        if (!string.IsNullOrEmpty(rule.TargetAddress) && 
            !rule.TargetAddress.Equals(packet.DestinationAddress) && 
            !rule.TargetAddress.Equals(packet.SourceAddress))
            return false;

        // Check target port
        if (rule.TargetPort != 0 && 
            rule.TargetPort != packet.DestinationPort && 
            rule.TargetPort != packet.SourcePort)
            return false;

        // Check direction
        if (packet.IsInbound && !rule.InterceptInbound)
            return false;
        if (!packet.IsInbound && !rule.InterceptOutbound)
            return false;

        return true;
    }

    private byte[] ModifyPacketContent(byte[] originalData, InterceptionRule rule)
    {
        try
        {
            var packet = Packet.ParsePacket(LinkLayers.Raw, originalData);
            var tcpPacket = packet.Extract<TcpPacket>();
            var udpPacket = packet.Extract<UdpPacket>();

            if (tcpPacket?.PayloadData != null)
            {
                var payload = Encoding.UTF8.GetString(tcpPacket.PayloadData);
                var modifiedPayload = payload.Replace(rule.ModificationPattern, rule.ReplacementContent);
                tcpPacket.PayloadData = Encoding.UTF8.GetBytes(modifiedPayload);
                
                // Recalculate checksums
                tcpPacket.UpdateTcpChecksum();
                var ipPacket = packet.Extract<IPPacket>();
                if (ipPacket != null)
                {
                    ipPacket.UpdateCalculatedValues();
                }
                
                return packet.Bytes;
            }
            else if (udpPacket?.PayloadData != null)
            {
                var payload = Encoding.UTF8.GetString(udpPacket.PayloadData);
                var modifiedPayload = payload.Replace(rule.ModificationPattern, rule.ReplacementContent);
                udpPacket.PayloadData = Encoding.UTF8.GetBytes(modifiedPayload);
                
                // Recalculate checksums
                udpPacket.UpdateUdpChecksum();
                var ipPacket = packet.Extract<IPPacket>();
                if (ipPacket != null)
                {
                    ipPacket.UpdateCalculatedValues();
                }
                
                return packet.Bytes;
            }
        }
        catch
        {
            // If modification fails, return original data
        }

        return originalData;
    }

    public void Dispose()
    {
        StopCapture();
    }
}
