# Network Capture Test Script
Write-Host "=== Network Capture Test ===" -ForegroundColor Green
Write-Host ""

# Check if running as administrator
$currentPrincipal = New-Object Security.Principal.WindowsPrincipal([Security.Principal.WindowsIdentity]::GetCurrent())
$isAdmin = $currentPrincipal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)

if (-not $isAdmin) {
    Write-Host "This test requires administrator privileges." -ForegroundColor Yellow
    Write-Host "Attempting to restart with administrator privileges..." -ForegroundColor Yellow
    
    try {
        Start-Process powershell -ArgumentList "-ExecutionPolicy Bypass -File `"$PSCommandPath`"" -Verb RunAs
        exit
    }
    catch {
        Write-Host "Failed to restart with administrator privileges." -ForegroundColor Red
        Write-Host "Please run this script as administrator manually." -ForegroundColor Red
        Read-Host "Press Enter to exit"
        exit
    }
}

Write-Host "✅ Running with administrator privileges" -ForegroundColor Green
Write-Host ""

# Check WinDivert files
$winDivertDll = "Native\WinDivert.dll"
$winDivertSys = "Native\WinDivert.sys"

Write-Host "Checking WinDivert files..." -ForegroundColor Yellow
Write-Host "WinDivert.dll: $(if (Test-Path $winDivertDll) { '✅ Found' } else { '❌ Missing' })"
Write-Host "WinDivert.sys: $(if (Test-Path $winDivertSys) { '✅ Found' } else { '❌ Missing' })"

if (-not (Test-Path $winDivertDll) -or -not (Test-Path $winDivertSys)) {
    Write-Host ""
    Write-Host "❌ WinDivert files are missing!" -ForegroundColor Red
    Write-Host "Please run setup-windivert.ps1 first to download WinDivert." -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit
}

Write-Host ""
Write-Host "🚀 Starting network capture test..." -ForegroundColor Green
Write-Host ""

try {
    # Build and run the test
    dotnet run --project QuickTest.csproj
}
catch {
    Write-Host "❌ Error running test: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Read-Host "Press Enter to exit"
