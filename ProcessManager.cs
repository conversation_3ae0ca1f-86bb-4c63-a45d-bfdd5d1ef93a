using System.Diagnostics;
using System.Management;
using System.Net.NetworkInformation;
using NetworkInterceptor.Models;

namespace NetworkInterceptor;

public class ProcessManager
{
    public List<ProcessInfo> GetAllProcesses()
    {
        var processes = new List<ProcessInfo>();
        var systemProcesses = Process.GetProcesses();

        foreach (var process in systemProcesses)
        {
            try
            {
                var processInfo = new ProcessInfo
                {
                    ProcessId = process.Id,
                    ProcessName = process.ProcessName,
                    StartTime = process.StartTime,
                    WorkingSet = process.WorkingSet64
                };

                try
                {
                    processInfo.ExecutablePath = process.MainModule?.FileName ?? string.Empty;
                    processInfo.WindowTitle = process.MainWindowTitle;
                }
                catch
                {
                    // Some processes may not allow access to these properties
                }

                processInfo.Connections = GetProcessConnections(process.Id);
                processes.Add(processInfo);
            }
            catch
            {
                // Skip processes that can't be accessed
            }
        }

        return processes.OrderBy(p => p.ProcessName).ToList();
    }

    public ProcessInfo? GetProcessById(int processId)
    {
        try
        {
            var process = Process.GetProcessById(processId);
            var processInfo = new ProcessInfo
            {
                ProcessId = process.Id,
                ProcessName = process.ProcessName,
                StartTime = process.StartTime,
                WorkingSet = process.WorkingSet64
            };

            try
            {
                processInfo.ExecutablePath = process.MainModule?.FileName ?? string.Empty;
                processInfo.WindowTitle = process.MainWindowTitle;
            }
            catch
            {
                // Some processes may not allow access to these properties
            }

            processInfo.Connections = GetProcessConnections(process.Id);
            return processInfo;
        }
        catch
        {
            return null;
        }
    }

    public List<ProcessInfo> GetProcessesByName(string processName)
    {
        var processes = new List<ProcessInfo>();
        var systemProcesses = Process.GetProcessesByName(processName);

        foreach (var process in systemProcesses)
        {
            try
            {
                var processInfo = new ProcessInfo
                {
                    ProcessId = process.Id,
                    ProcessName = process.ProcessName,
                    StartTime = process.StartTime,
                    WorkingSet = process.WorkingSet64
                };

                try
                {
                    processInfo.ExecutablePath = process.MainModule?.FileName ?? string.Empty;
                    processInfo.WindowTitle = process.MainWindowTitle;
                }
                catch
                {
                    // Some processes may not allow access to these properties
                }

                processInfo.Connections = GetProcessConnections(process.Id);
                processes.Add(processInfo);
            }
            catch
            {
                // Skip processes that can't be accessed
            }
        }

        return processes;
    }

    private List<NetworkConnection> GetProcessConnections(int processId)
    {
        var connections = new List<NetworkConnection>();

        try
        {
            // Get TCP connections
            var tcpConnections = IPGlobalProperties.GetIPGlobalProperties().GetActiveTcpConnections();
            foreach (var conn in tcpConnections)
            {
                // Note: .NET doesn't provide direct process ID mapping for connections
                // This would require additional WMI queries or netstat parsing
                connections.Add(new NetworkConnection
                {
                    Protocol = "TCP",
                    LocalAddress = conn.LocalEndPoint.Address.ToString(),
                    LocalPort = conn.LocalEndPoint.Port,
                    RemoteAddress = conn.RemoteEndPoint.Address.ToString(),
                    RemotePort = conn.RemoteEndPoint.Port,
                    State = conn.State.ToString()
                });
            }

            // Get UDP listeners
            var udpListeners = IPGlobalProperties.GetIPGlobalProperties().GetActiveUdpListeners();
            foreach (var listener in udpListeners)
            {
                connections.Add(new NetworkConnection
                {
                    Protocol = "UDP",
                    LocalAddress = listener.Address.ToString(),
                    LocalPort = listener.Port,
                    RemoteAddress = "0.0.0.0",
                    RemotePort = 0,
                    State = "LISTENING"
                });
            }
        }
        catch
        {
            // If we can't get connection info, return empty list
        }

        return connections;
    }

    public bool IsProcessRunning(int processId)
    {
        try
        {
            var process = Process.GetProcessById(processId);
            return !process.HasExited;
        }
        catch
        {
            return false;
        }
    }

    public void KillProcess(int processId)
    {
        try
        {
            var process = Process.GetProcessById(processId);
            process.Kill();
        }
        catch (Exception ex)
        {
            throw new InvalidOperationException($"Failed to kill process {processId}: {ex.Message}");
        }
    }
}
