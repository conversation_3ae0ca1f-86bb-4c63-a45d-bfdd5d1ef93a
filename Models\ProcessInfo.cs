namespace NetworkInterceptor.Models;

public class ProcessInfo
{
    public int ProcessId { get; set; }
    public string ProcessName { get; set; } = string.Empty;
    public string ExecutablePath { get; set; } = string.Empty;
    public DateTime StartTime { get; set; }
    public long WorkingSet { get; set; }
    public string WindowTitle { get; set; } = string.Empty;
    public List<NetworkConnection> Connections { get; set; } = new();
}

public class NetworkConnection
{
    public string Protocol { get; set; } = string.Empty;
    public string LocalAddress { get; set; } = string.Empty;
    public int LocalPort { get; set; }
    public string RemoteAddress { get; set; } = string.Empty;
    public int RemotePort { get; set; }
    public string State { get; set; } = string.Empty;
}

public class InterceptedPacket
{
    public DateTime Timestamp { get; set; }
    public int ProcessId { get; set; }
    public string Protocol { get; set; } = string.Empty;
    public string SourceAddress { get; set; } = string.Empty;
    public int SourcePort { get; set; }
    public string DestinationAddress { get; set; } = string.Empty;
    public int DestinationPort { get; set; }
    public byte[] Data { get; set; } = Array.Empty<byte>();
    public bool IsInbound { get; set; }
    public bool IsModified { get; set; }
}

public class InterceptionRule
{
    public int ProcessId { get; set; }
    public string Protocol { get; set; } = string.Empty;
    public string TargetAddress { get; set; } = string.Empty;
    public int TargetPort { get; set; }
    public bool InterceptInbound { get; set; } = true;
    public bool InterceptOutbound { get; set; } = true;
    public bool ModifyContent { get; set; } = false;
    public string ModificationPattern { get; set; } = string.Empty;
    public string ReplacementContent { get; set; } = string.Empty;
}
