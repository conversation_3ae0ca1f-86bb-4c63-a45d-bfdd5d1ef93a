<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
    <AllowUnsafeBlocks>true</AllowUnsafeBlocks>
    <Platforms>x64</Platforms>
    <RuntimeIdentifier>win-x64</RuntimeIdentifier>
    <StartupObject>TestNetworkMonitor</StartupObject>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="SharpPcap" Version="6.3.0" />
    <PackageReference Include="PacketDotNet" Version="1.4.8" />
    <PackageReference Include="BouncyCastle.Cryptography" Version="2.3.1" />
    <PackageReference Include="System.Management" Version="8.0.0" />
  </ItemGroup>



  <ItemGroup>
    <None Include="Native\**" CopyToOutputDirectory="PreserveNewest" />
  </ItemGroup>

</Project>
